{"name": "YAMTOKEN", "version": "0.2.0", "private": true, "dependencies": {"@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.15.14", "@mui/material": "^5.15.14", "@reduxjs/toolkit": "^2.2.1", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.91", "@types/react": "^18.2.67", "@types/react-dom": "^18.2.22", "@types/react-slick": "^0.23.8", "axios": "^1.6.8", "dayjs": "^1.11.10", "js-cookie": "^3.0.5", "react": "^18.2.0", "react-dom": "^18.2.0", "react-redux": "^9.1.0", "react-router-dom": "^6.22.3", "react-scripts": "5.0.1", "react-slick": "^0.29.0", "room-populate": "^1.0.17", "bitcoin-core": "^4.2.0", "slick-carousel": "^1.8.1", "socket.io-client": "^4.7.5", "typescript": "^4.9.5", "web-vitals": "^2.1.4", "concurrently": "^5.3.0", "npm-run-all": "^4.1.5", "nodemon": "^3.0.3", "gravatar": "^1.8.1", "html2canvas": "^1.4.1", "jsonwebtoken": "^8.5.1", "matter-js": "^0.19.0", "mongoose": "^5.11.8", "bcryptjs": "^2.4.3", "buffer": "^6.0.3", "config": "^3.3.3", "dotenv": "^16.4.5", "express": "^4.17.1", "express-validator": "^6.8.1", "swr": "^2.2.4", "three": "^0.158.0", "zustand": "^4.4.1"}, "scripts": {"server": "node server", "start": "npm-run-all --parallel server client ", "dev": "npm-run-all --parallel server client", "client": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}