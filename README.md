## DeFi Road Map

We are building an innovative DeFi platform similar to [Yam on RealT](https://staging-yam.realtoken.network), aiming to make decentralized finance accessible and efficient.
Our project is in advanced stages, with initial smart contracts and an frontend UI already implemented.
The role will focus on finalizing and enhancing our platform’s user interface, integrating wallet functionality, and developing a marketplace for token transactions.

## Environment

If you meet any error while running the project, check the options bellow. And if you are using Windows, it is recommended to run the project using powershell or cmd.

Node verion: v18 or later

OS: Mac, Linux, Windows(An unexpected issue may arise)
